import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { History, Heart, BarChart3, Upload } from 'lucide-react';

// Datos de prueba simples
const mockRecentAnalyses = [
  {
    id: 'test-1',
    created_at: new Date().toISOString(),
    original_filename: 'landing-page.png',
    overall_score: 85,
    is_favorite: false,
    custom_name: 'Landing Page Principal',
    view_count: 5,
    regeneration_count: 1
  },
  {
    id: 'test-2',
    created_at: new Date(Date.now() - 86400000).toISOString(),
    original_filename: 'mobile-app.jpg',
    overall_score: 72,
    is_favorite: true,
    custom_name: 'App Móvil Dashboard',
    view_count: 12,
    regeneration_count: 0
  }
];

const mockFavoriteAnalyses = mockRecentAnalyses.filter(a => a.is_favorite);

export default function DesignComplexityAnalyzerTest() {
  const [activeTab, setActiveTab] = useState("analyze");
  const [recentAnalyses, setRecentAnalyses] = useState(mockRecentAnalyses);
  const [favoriteAnalyses, setFavoriteAnalyses] = useState(mockFavoriteAnalyses);

  console.log('🔍 DesignComplexityAnalyzerTest component is rendering');
  console.log('📊 Mock data:', { recent: recentAnalyses.length, favorites: favoriteAnalyses.length });

  const handleToggleFavorite = (analysisId: string) => {
    console.log('❤️ Toggling favorite for:', analysisId);
    // Simular toggle de favorito
    setRecentAnalyses(prev => prev.map(analysis =>
      analysis.id === analysisId
        ? { ...analysis, is_favorite: !analysis.is_favorite }
        : analysis
    ));

    // Actualizar favoritos
    const updatedAnalysis = recentAnalyses.find(a => a.id === analysisId);
    if (updatedAnalysis) {
      if (updatedAnalysis.is_favorite) {
        setFavoriteAnalyses(prev => prev.filter(a => a.id !== analysisId));
      } else {
        setFavoriteAnalyses(prev => [...prev, { ...updatedAnalysis, is_favorite: true }]);
      }
    }
  };

  const handleLoadAnalysis = (analysisId: string) => {
    console.log('📂 Loading analysis:', analysisId);
    setActiveTab("analyze");
  };

  const handleRegenerateAnalysis = (analysisId: string) => {
    console.log('🔄 Regenerating analysis:', analysisId);
    setActiveTab("analyze");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Debug indicator */}
      <div className="fixed top-4 right-4 bg-green-500 text-white px-3 py-1 rounded text-sm z-50">
        ✅ Test Component Loaded - Recent: {recentAnalyses.length}, Favorites: {favoriteAnalyses.length}
      </div>
      
      <div className="container mx-auto px-6 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Analizador de Complejidad Visual
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Evalúa la complejidad visual de tus diseños con IA avanzada y
            obtén recomendaciones prácticas para optimizar su impacto
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="analyze">Análisis de Complejidad</TabsTrigger>
            <TabsTrigger value="theory" disabled>
              Resultados Detallados
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              Historial ({recentAnalyses.length})
            </TabsTrigger>
            <TabsTrigger value="favorites" className="flex items-center gap-2">
              <Heart className="h-4 w-4" />
              Favoritos ({favoriteAnalyses.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="analyze" className="space-y-4">
            <Card className="border-2 border-gray-200 shadow-sm">
              <CardHeader className="border-b border-gray-100 bg-gray-50 rounded-t-lg">
                <CardTitle className="text-blue-600 flex items-center gap-2">
                  <Upload className="h-5 w-5 text-blue-600" />
                  Subir Diseño
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-6">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center bg-gray-50">
                  <Upload className="h-10 w-10 text-gray-400 mx-auto mb-4" />
                  <h3 className="font-medium text-gray-700 mb-1">
                    Arrastra y suelta tu diseño
                  </h3>
                  <p className="text-gray-500 text-sm mb-4">
                    O haz clic para seleccionar
                  </p>
                  <p className="text-xs text-gray-400">
                    PNG, JPG o GIF hasta 5MB
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Pestaña de Historial */}
          <TabsContent value="history" className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">
                  Historial de Análisis
                </h2>
                <Badge variant="secondary">
                  {recentAnalyses.length} análisis
                </Badge>
              </div>

              {isLoadingAnalyses ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-500 mt-2">Cargando análisis...</p>
                </div>
              ) : recentAnalyses.length === 0 ? (
                <Card className="p-8 text-center">
                  <History className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="font-medium text-gray-700 mb-2">
                    No hay análisis en tu historial
                  </h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Los análisis que realices aparecerán aquí
                  </p>
                  <Button onClick={() => setActiveTab("analyze")}>
                    Realizar primer análisis
                  </Button>
                </Card>
              ) : (
                <div className="grid gap-4">
                  {recentAnalyses.map((analysis) => (
                    <Card key={analysis.id} className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold">{analysis.custom_name}</h3>
                          <p className="text-sm text-gray-600">{analysis.original_filename}</p>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant="outline">
                              <BarChart3 className="h-3 w-3 mr-1" />
                              {analysis.overall_score}/100
                            </Badge>
                            {analysis.is_favorite && (
                              <Badge variant="outline" className="text-red-600">
                                <Heart className="h-3 w-3 mr-1 fill-current" />
                                Favorito
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleToggleFavorite(analysis.id)}
                          >
                            <Heart className={`h-4 w-4 ${analysis.is_favorite ? 'fill-current text-red-600' : ''}`} />
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleLoadAnalysis(analysis.id)}
                          >
                            Cargar
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          {/* Pestaña de Favoritos */}
          <TabsContent value="favorites" className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">
                  Análisis Favoritos
                </h2>
                <Badge variant="secondary">
                  {favoriteAnalyses.length} favoritos
                </Badge>
              </div>

              {isLoadingAnalyses ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-500 mt-2">Cargando favoritos...</p>
                </div>
              ) : favoriteAnalyses.length === 0 ? (
                <Card className="p-8 text-center">
                  <Heart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="font-medium text-gray-700 mb-2">
                    No tienes análisis favoritos
                  </h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Marca análisis como favoritos para acceder rápidamente a ellos
                  </p>
                  <Button onClick={() => setActiveTab("history")}>
                    Ver historial
                  </Button>
                </Card>
              ) : (
                <div className="grid gap-4">
                  {favoriteAnalyses.map((analysis) => (
                    <Card key={analysis.id} className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold">{analysis.custom_name}</h3>
                          <p className="text-sm text-gray-600">{analysis.original_filename}</p>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant="outline">
                              <BarChart3 className="h-3 w-3 mr-1" />
                              {analysis.overall_score}/100
                            </Badge>
                            <Badge variant="outline" className="text-red-600">
                              <Heart className="h-3 w-3 mr-1 fill-current" />
                              Favorito
                            </Badge>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleToggleFavorite(analysis.id)}
                            className="text-red-600"
                          >
                            <Heart className="h-4 w-4 fill-current" />
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleLoadAnalysis(analysis.id)}
                          >
                            Cargar
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
